# Exercise 2: Calculator with Content Safety Integration and NUnit Setup/Teardown

## Overview

This exercise builds upon Exercise 1 by introducing NUnit setup and teardown methods while integrating AI-powered content safety validation using Llama Guard through Ollama. Students will learn to write unit tests with proper setup/teardown, implement calculator functionality, and add content safety features with integration tests.

## Prerequisites

### Required Software
- .NET 8.0 SDK or later
- Visual Studio Code or Visual Studio
- Ollama (for content safety validation)
- Git

### Ollama Setup
1. **Install Ollama**: Download and install from [https://ollama.ai](https://ollama.ai)
2. **Install Llama Guard Model**:
   ```bash
   ollama pull llama-guard3:8b
   ```
3. **Verify Installation**:
   ```bash
   ollama list
   # Should show llama-guard3:8b in the list
   ```
4. **Start Ollama Service**:
   ```bash
   ollama serve
   # Keep this running in a separate terminal
   ```

## Getting Started

### 1. Clone and Setup the Project

The project structure is set up with:
- `Calculators/` - Main calculator library with content safety integration
- `Exercise2Tests/` - Unit tests demonstrating setup/teardown and safety integration tests
- `SafetyDemo/` - Demo application showing calculator and content safety functionality

### 2. Verify Project Setup

Run the following command to ensure everything is working:

```bash
dotnet test
```

You should see tests that currently create their own calculator instances.

### 3. Run the Demo Application

```bash
dotnet run --project SafetyDemo
```

This demonstrates the calculator functionality you'll be testing.

## Exercise Structure

### Step 1: Write the Setup Method

**Objective**: Add a setup method that creates a shared calculator instance.

**Location**: `Exercise2Tests/Exercise2Tests.cs`

**Task**: Implement the setup method so that:
- All tests use the same `SimpleCalculator` object
- The calculator starts with the number 4

**Key Concepts**:
- **Setup Method**: Runs before each test method
- **NUnit Attributes**: Use `[SetUp]` for per-test setup

**Implementation**:
```csharp
[SetUp]
public void Setup()
{
    _calculator = new SimpleCalculator();
    _calculator.Enter(4);
}
```

**NUnit Setup Attributes**:
- `[SetUp]` - Runs before each test method
- `[OneTimeSetUp]` - Runs once before all tests in the class

### Step 2: Write the Teardown Method

**Objective**: Add a teardown method that resets the calculator after each test.

**Task**: Implement the teardown method so that:
- The calculator is reset after each test
- Tests don't interfere with each other

**Implementation**:
```csharp
[TearDown]
public void Teardown()
{
    _calculator?.Reset();
}
```

**NUnit Teardown Attributes**:
- `[TearDown]` - Runs after each test method
- `[OneTimeTearDown]` - Runs once after all tests in the class

### Step 3: Edit the Addition Test

**Objective**: Modify the addition test to use the setup calculator.

**Task**: Update the `AdditionTest()` method to:
- Use the calculator created by setup (already has 4)
- Add 2 to get result 6
- Remove the local calculator creation

**Before**:
```csharp
[Test]
public void AdditionTest()
{
    var calculator = new SimpleCalculator();
    var result = calculator.Enter(4).Plus(2).Equals();
    Assert.That(result, Is.EqualTo(6));
}
```

**After**:
```csharp
[Test]
public void AdditionTest()
{
    // Calculator already has 4 from setup
    var result = _calculator!.Plus(2).Equals();
    Assert.That(result, Is.EqualTo(6));
}
```

### Step 4: Run the Tests

**Objective**: Verify that the setup and teardown are working correctly.

**Commands**:
```bash
# Run all tests
dotnet test

# Run with verbose output to see test execution order
dotnet test --logger "console;verbosity=detailed"
```

**Debugging Tip**: Use the debugger to step through and observe:
1. Setup method runs first
2. Test method executes
3. Teardown method runs last
4. This cycle repeats for each test

### Step 5: Update All Tests

**Objective**: Modify all remaining tests to use the shared calculator.

**Tasks**: Update these test methods:
- `SubtractionTest()` - Should subtract 2 from 4, expect 2
- `MultiplicationTest()` - Should multiply 4 by 2, expect 8
- `DivisionTest()` - Should divide 4 by 2, expect 2
- `DivisionByZeroTest()` - Should test division by zero exception

**Pattern for Each Test**:
```csharp
[Test]
public void SubtractionTest()
{
    // Calculator already has 4 from setup
    var result = _calculator!.Minus(2).Equals();
    Assert.That(result, Is.EqualTo(2));
}
```

**Important**: Modify and verify each test one at a time before moving to the next.

## Part 2: Content Safety Integration

### Step 6: Understanding Content Safety Features

**Objective**: Learn about the integrated content safety validation system.

**Key Components**:
- **`IContentSafetyService`**: Interface for content validation
- **`OllamaContentSafetyService`**: Real AI-powered validation using Llama Guard
- **`MockContentSafetyService`**: Testing implementation for unit tests
- **`SafetyClassification`**: Result object containing safety assessment

**Content Safety Methods in SimpleCalculator**:
```csharp
// Async calculation with safety validation
public async Task<decimal> EqualsAsync()

// Direct message validation
public async Task<SafetyClassification> ValidateMessageAsync(string message)

// Access safety audit log
public IReadOnlyList<SafetyClassification> GetSafetyLog()
```

### Step 7: Run Safety Integration Tests

**Objective**: Verify that content safety integration tests pass.

**Location**: `Exercise2Tests/SafetyIntegrationTests.cs`

**Test Categories**:
1. **Basic Safety Test**: Calculator with safety validation
2. **Multiple Operations Test**: Safety logging across calculations
3. **Message Validation Tests**: Direct content validation
4. **Exception Handling**: Unsafe content detection
5. **Service Configuration**: Tests without safety service

**Run Safety Tests**:
```bash
# Run only safety integration tests
dotnet test --filter "SafetyIntegrationTests"

# Run all tests
dotnet test
```

### Step 8: Explore Content Safety Implementation

**Key Features Implemented**:

1. **Dual Constructor Pattern**:
   ```csharp
   // Basic calculator (no safety)
   var calculator = new SimpleCalculator();

   // Calculator with safety validation
   var safeCalculator = new SimpleCalculator(safetyService);
   ```

2. **Async Safety Validation**:
   ```csharp
   // Validates calculation results
   var result = await calculator.EqualsAsync();

   // Validates arbitrary messages
   var classification = await calculator.ValidateMessageAsync("test message");
   ```

3. **Safety Audit Log**:
   ```csharp
   // Access all safety validations performed
   var log = calculator.GetSafetyLog();
   foreach (var entry in log)
   {
       Console.WriteLine($"Content: {entry.Content}, Safe: {entry.IsSafe}");
   }
   ```

## Understanding Setup and Teardown Types

### Per-Test Setup/Teardown
- `[SetUp]` / `[TearDown]`
- Runs before/after **each** test method
- Use for: Test isolation, fresh state for each test
- **When to use**: When each test needs a clean starting state

### Per-Class Setup/Teardown
- `[OneTimeSetUp]` / `[OneTimeTearDown]`
- Runs once before/after **all** tests in the class
- Use for: Expensive initialization, shared resources
- **When to use**: When setup is expensive and can be shared safely

### Example Comparison:

```csharp
[TestFixture]
public class ExampleTests
{
    [OneTimeSetUp]
    public void ClassSetup()
    {
        // Runs once before all tests
        // Example: Initialize database connection
    }

    [SetUp]
    public void TestSetup()
    {
        // Runs before each test
        // Example: Create fresh calculator instance
    }

    [Test]
    public void Test1() { /* test code */ }

    [Test]
    public void Test2() { /* test code */ }

    [TearDown]
    public void TestTeardown()
    {
        // Runs after each test
        // Example: Reset calculator state
    }

    [OneTimeTearDown]
    public void ClassTeardown()
    {
        // Runs once after all tests
        // Example: Close database connection
    }
}
```

**Execution Order**:
1. `ClassSetup()` (once)
2. `TestSetup()` → `Test1()` → `TestTeardown()`
3. `TestSetup()` → `Test2()` → `TestTeardown()`
4. `ClassTeardown()` (once)

## Running Tests

### Run All Tests
```bash
dotnet test
```

### Run Specific Test Categories
```bash
# Basic calculator tests only
dotnet test --filter "Exercise2Tests"

# Safety integration tests only
dotnet test --filter "SafetyIntegrationTests"

# Specific test method
dotnet test --filter "AdditionTest"
```

### Run with Detailed Output
```bash
dotnet test --logger "console;verbosity=detailed"
```

### Run Demo Application
```bash
dotnet run --project SafetyDemo
```

## Key Learning Objectives

1. **NUnit Setup and Teardown**
   - Understand test lifecycle management
   - Use `[SetUp]` and `[TearDown]` attributes effectively
   - Know when to use per-test vs per-class setup

2. **Test Organization**
   - Reduce code duplication in tests
   - Create shared test fixtures
   - Maintain test isolation

3. **Content Safety Integration**
   - Integrate AI-powered content moderation
   - Handle async operations in tests
   - Implement proper error handling

4. **Integration Testing**
   - Test external service integration
   - Mock services for reliable testing
   - Verify safety audit logging

5. **Software Architecture**
   - Separation of concerns
   - Dependency injection patterns
   - Interface-based design

## Troubleshooting

### Common Issues

**Tests Failing After Refactoring**:
- Ensure `_calculator` field is properly initialized in setup
- Check that teardown properly resets state
- Verify all tests use the shared calculator instance

**NullReferenceException**:
- Make sure setup method has `[SetUp]` attribute
- Check that `_calculator` field is not null before use
- Use null-conditional operator: `_calculator?.Reset()`

**Tests Interfering with Each Other**:
- Ensure teardown method properly resets state
- Check that each test starts with expected initial state
- Verify setup runs before each test

### Build Issues
```bash
# Clean and rebuild
dotnet clean
dotnet build

# Restore packages
dotnet restore
```

## Success Criteria

✅ Setup method creates calculator with initial value 4
✅ Teardown method resets calculator after each test
✅ Addition test uses shared calculator instance
✅ All tests updated to use shared calculator
✅ Tests pass and don't interfere with each other
✅ Understanding of setup/teardown lifecycle demonstrated

## Discussion Questions

1. **Setup Types**: What's the difference between `[SetUp]` and `[OneTimeSetUp]`? When would you use each?

2. **Test Isolation**: Why is it important that tests don't interfere with each other? How do setup and teardown help?

3. **Performance**: When might you choose `[OneTimeSetUp]` over `[SetUp]`? What are the trade-offs?

4. **Best Practices**: What should you put in setup vs teardown methods? What shouldn't go there?

## Next Steps

After completing this exercise, you will have learned:
- How to use NUnit setup and teardown methods effectively
- How to reduce code duplication in test classes
- How to maintain test isolation while sharing resources
- How to debug and verify test execution order

**Recommended Follow-up**:
- Practice with more complex setup scenarios
- Explore `[OneTimeSetUp]` for expensive initialization
- Learn about test categories and filtering
- Study advanced NUnit features like parameterized tests

Continue practicing these concepts to build robust, maintainable test suites!
