# Exercise 2: NUnit Setup and Teardown Methods

## Overview

This exercise focuses on NUnit setup and teardown methods to improve test organization and reduce code duplication. Students will learn about test lifecycle management, the different types of setup/teardown attributes in NUnit, and how to refactor tests to use shared test fixtures.

## Prerequisites

### Required Software
- .NET 8.0 SDK or later
- Visual Studio Code or Visual Studio
- Git

## Getting Started

### 1. <PERSON><PERSON> and Setup the Project

The project structure is set up with:
- `Calculators/` - Main calculator library
- `Exercise2Tests/` - Unit tests demonstrating setup and teardown
- `SafetyDemo/` - Demo application showing calculator functionality

### 2. Verify Project Setup

Run the following command to ensure everything is working:

```bash
dotnet test
```

You should see tests that currently create their own calculator instances.

### 3. Run the Demo Application

```bash
dotnet run --project SafetyDemo
```

This demonstrates the calculator functionality you'll be testing.

## Exercise Structure

### Step 1: Write the Setup Method

**Objective**: Add a setup method that creates a shared calculator instance.

**Location**: `Exercise2Tests/Exercise2Tests.cs`

**Task**: Implement the setup method so that:
- All tests use the same `SimpleCalculator` object
- The calculator starts with the number 4

**Key Concepts**:
- **Setup Method**: Runs before each test method
- **NUnit Attributes**: Use `[SetUp]` for per-test setup

**Implementation**:
```csharp
[SetUp]
public void Setup()
{
    _calculator = new SimpleCalculator();
    _calculator.Enter(4);
}
```

**NUnit Setup Attributes**:
- `[SetUp]` - Runs before each test method
- `[OneTimeSetUp]` - Runs once before all tests in the class

### Step 2: Write the Teardown Method

**Objective**: Add a teardown method that resets the calculator after each test.

**Task**: Implement the teardown method so that:
- The calculator is reset after each test
- Tests don't interfere with each other

**Implementation**:
```csharp
[TearDown]
public void Teardown()
{
    _calculator?.Reset();
}
```

**NUnit Teardown Attributes**:
- `[TearDown]` - Runs after each test method
- `[OneTimeTearDown]` - Runs once after all tests in the class

### Step 3: Edit the Addition Test

**Objective**: Modify the addition test to use the setup calculator.

**Task**: Update the `AdditionTest()` method to:
- Use the calculator created by setup (already has 4)
- Add 2 to get result 6
- Remove the local calculator creation

**Before**:
```csharp
[Test]
public void AdditionTest()
{
    var calculator = new SimpleCalculator();
    var result = calculator.Enter(4).Plus(2).Equals();
    Assert.That(result, Is.EqualTo(6));
}
```

**After**:
```csharp
[Test]
public void AdditionTest()
{
    // Calculator already has 4 from setup
    var result = _calculator!.Plus(2).Equals();
    Assert.That(result, Is.EqualTo(6));
}
```

### Step 4: Run the Tests

**Objective**: Verify that the setup and teardown are working correctly.

**Commands**:
```bash
# Run all tests
dotnet test

# Run with verbose output to see test execution order
dotnet test --logger "console;verbosity=detailed"
```

**Debugging Tip**: Use the debugger to step through and observe:
1. Setup method runs first
2. Test method executes
3. Teardown method runs last
4. This cycle repeats for each test

### Step 5: Update All Tests

**Objective**: Modify all remaining tests to use the shared calculator.

**Tasks**: Update these test methods:
- `SubtractionTest()` - Should subtract 2 from 4, expect 2
- `MultiplicationTest()` - Should multiply 4 by 2, expect 8
- `DivisionTest()` - Should divide 4 by 2, expect 2
- `DivisionByZeroTest()` - Should test division by zero exception

**Pattern for Each Test**:
```csharp
[Test]
public void SubtractionTest()
{
    // Calculator already has 4 from setup
    var result = _calculator!.Minus(2).Equals();
    Assert.That(result, Is.EqualTo(2));
}
```

**Important**: Modify and verify each test one at a time before moving to the next.

## Understanding Setup and Teardown Types

### Per-Test Setup/Teardown
- `[SetUp]` / `[TearDown]`
- Runs before/after **each** test method
- Use for: Test isolation, fresh state for each test
- **When to use**: When each test needs a clean starting state

### Per-Class Setup/Teardown
- `[OneTimeSetUp]` / `[OneTimeTearDown]`
- Runs once before/after **all** tests in the class
- Use for: Expensive initialization, shared resources
- **When to use**: When setup is expensive and can be shared safely

### Example Comparison:

```csharp
[TestFixture]
public class ExampleTests
{
    [OneTimeSetUp]
    public void ClassSetup()
    {
        // Runs once before all tests
        // Example: Initialize database connection
    }

    [SetUp]
    public void TestSetup()
    {
        // Runs before each test
        // Example: Create fresh calculator instance
    }

    [Test]
    public void Test1() { /* test code */ }

    [Test]
    public void Test2() { /* test code */ }

    [TearDown]
    public void TestTeardown()
    {
        // Runs after each test
        // Example: Reset calculator state
    }

    [OneTimeTearDown]
    public void ClassTeardown()
    {
        // Runs once after all tests
        // Example: Close database connection
    }
}
```

**Execution Order**:
1. `ClassSetup()` (once)
2. `TestSetup()` → `Test1()` → `TestTeardown()`
3. `TestSetup()` → `Test2()` → `TestTeardown()`
4. `ClassTeardown()` (once)

## Running Tests

### Run All Tests
```bash
dotnet test
```

### Run with Detailed Output
```bash
dotnet test --logger "console;verbosity=detailed"
```

### Run Specific Test
```bash
dotnet test --filter "AdditionTest"
```

### Run Demo Application
```bash
dotnet run --project SafetyDemo
```

## Key Learning Objectives

1. **NUnit Setup and Teardown**
   - Understand test lifecycle management
   - Use `[SetUp]` and `[TearDown]` attributes effectively
   - Know when to use per-test vs per-class setup

2. **Test Organization**
   - Reduce code duplication in tests
   - Create shared test fixtures
   - Maintain test isolation

3. **Debugging Test Execution**
   - Use debugger to observe test execution order
   - Understand test method lifecycle
   - Verify setup and teardown behavior

4. **Best Practices**
   - When to use different setup/teardown types
   - How to structure test classes for maintainability
   - Ensuring tests don't interfere with each other

## Troubleshooting

### Common Issues

**Tests Failing After Refactoring**:
- Ensure `_calculator` field is properly initialized in setup
- Check that teardown properly resets state
- Verify all tests use the shared calculator instance

**NullReferenceException**:
- Make sure setup method has `[SetUp]` attribute
- Check that `_calculator` field is not null before use
- Use null-conditional operator: `_calculator?.Reset()`

**Tests Interfering with Each Other**:
- Ensure teardown method properly resets state
- Check that each test starts with expected initial state
- Verify setup runs before each test

### Build Issues
```bash
# Clean and rebuild
dotnet clean
dotnet build

# Restore packages
dotnet restore
```

## Success Criteria

✅ Setup method creates calculator with initial value 4
✅ Teardown method resets calculator after each test
✅ Addition test uses shared calculator instance
✅ All tests updated to use shared calculator
✅ Tests pass and don't interfere with each other
✅ Understanding of setup/teardown lifecycle demonstrated

## Discussion Questions

1. **Setup Types**: What's the difference between `[SetUp]` and `[OneTimeSetUp]`? When would you use each?

2. **Test Isolation**: Why is it important that tests don't interfere with each other? How do setup and teardown help?

3. **Performance**: When might you choose `[OneTimeSetUp]` over `[SetUp]`? What are the trade-offs?

4. **Best Practices**: What should you put in setup vs teardown methods? What shouldn't go there?

## Next Steps

After completing this exercise, you will have learned:
- How to use NUnit setup and teardown methods effectively
- How to reduce code duplication in test classes
- How to maintain test isolation while sharing resources
- How to debug and verify test execution order

**Recommended Follow-up**:
- Practice with more complex setup scenarios
- Explore `[OneTimeSetUp]` for expensive initialization
- Learn about test categories and filtering
- Study advanced NUnit features like parameterized tests

Continue practicing these concepts to build robust, maintainable test suites!
