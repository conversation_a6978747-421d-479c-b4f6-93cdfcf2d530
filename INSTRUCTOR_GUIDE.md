# Exercise 2: Instructor Guide

## Overview
This exercise teaches students about NUnit setup and teardown methods through hands-on refactoring of calculator tests.

## Learning Objectives
- Understand NUnit test lifecycle management
- Learn the difference between `[SetUp]`/`[TearDown]` and `[OneTimeSetUp]`/`[OneTimeTearDown]`
- Practice refactoring tests to reduce code duplication
- Experience debugging test execution order

## Project Structure
```
exercise-2-calculator/
├── Calculators/                    # Calculator library
│   ├── SimpleCalculator.cs         # Main calculator class
│   └── ContentSafety/              # Future content safety features
├── Exercise2Tests/                 # Student workspace
│   ├── Exercise2Tests.cs           # Tests to be modified by students
│   └── Exercise2TestsCompleted.cs  # Reference implementation (hidden from students)
├── SafetyDemo/                     # Demo application
└── README.md                       # Student instructions
```

## Pre-Exercise Setup
1. Ensure .NET 8.0 SDK is installed
2. Verify project builds: `dotnet build`
3. Verify tests run: `dotnet test`
4. Check demo works: `dotnet run --project SafetyDemo`

## Exercise Flow

### Step 1: Setup Method (15 minutes)
**Student Task**: Add `[SetUp]` method to create shared calculator with initial value 4

**Key Teaching Points**:
- `[SetUp]` runs before each test
- Shared state vs test isolation
- Field initialization patterns

**Expected Implementation**:
```csharp
[SetUp]
public void Setup()
{
    _calculator = new SimpleCalculator();
    _calculator.Enter(4);
}
```

### Step 2: Teardown Method (10 minutes)
**Student Task**: Add `[TearDown]` method to reset calculator

**Key Teaching Points**:
- `[TearDown]` runs after each test
- Cleanup responsibilities
- Null-conditional operators

**Expected Implementation**:
```csharp
[TearDown]
public void Teardown()
{
    _calculator?.Reset();
}
```

### Step 3: Refactor Addition Test (10 minutes)
**Student Task**: Modify AdditionTest to use shared calculator

**Key Teaching Points**:
- Removing code duplication
- Trusting setup to provide initial state
- Null-forgiving operator usage

### Step 4: Verify Execution Order (10 minutes)
**Student Task**: Run tests with debugger to observe lifecycle

**Key Teaching Points**:
- Test execution lifecycle
- Setup → Test → Teardown cycle
- Debugging test execution

### Step 5: Refactor All Tests (20 minutes)
**Student Task**: Update remaining tests one by one

**Key Teaching Points**:
- Incremental refactoring approach
- Test isolation verification
- Consistent patterns across tests

## Common Student Issues

### Issue 1: NullReferenceException
**Cause**: Missing `[SetUp]` attribute or incorrect field access
**Solution**: Verify attribute is present and field is initialized

### Issue 2: Tests Interfering
**Cause**: Missing or incorrect teardown
**Solution**: Ensure `Reset()` is called in teardown

### Issue 3: Compilation Errors
**Cause**: Incorrect null handling
**Solution**: Use `!` operator or null-conditional operators

## Discussion Topics

### Setup Types Comparison
Use whiteboard to show execution order:
```
[OneTimeSetUp]     ← Once per class
  [SetUp]          ← Before each test
    Test1()
  [TearDown]       ← After each test
  [SetUp]
    Test2()
  [TearDown]
[OneTimeTearDown]  ← Once per class
```

### When to Use Each Type
- **Per-test**: Clean state, lightweight objects
- **Per-class**: Expensive setup, shared resources
- **Trade-offs**: Performance vs isolation

## Assessment Criteria
- [ ] Setup method correctly initializes calculator with value 4
- [ ] Teardown method properly resets calculator state
- [ ] All tests refactored to use shared calculator
- [ ] Tests pass and don't interfere with each other
- [ ] Student can explain setup/teardown lifecycle
- [ ] Student understands when to use different setup types

## Extension Activities
1. **Performance Comparison**: Time tests with/without setup
2. **OneTimeSetUp Practice**: Refactor to use class-level setup
3. **Complex Scenarios**: Add tests requiring different initial states
4. **Error Handling**: What happens if setup fails?

## Troubleshooting Commands
```bash
# Clean and rebuild
dotnet clean && dotnet build

# Run specific test class
dotnet test --filter "Exercise2Tests"

# Run with detailed output
dotnet test --logger "console;verbosity=detailed"

# Check project structure
find . -name "*.cs" -type f
```

## Time Allocation
- **Total**: 75 minutes
- **Setup method**: 15 minutes
- **Teardown method**: 10 minutes
- **Refactor addition test**: 10 minutes
- **Verify execution**: 10 minutes
- **Refactor all tests**: 20 minutes
- **Discussion**: 10 minutes

## Success Indicators
- All tests pass (10/10 in verbose output)
- No code duplication in test methods
- Students can explain execution order
- Students understand setup/teardown trade-offs
