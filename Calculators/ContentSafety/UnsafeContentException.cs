using System;

namespace Calculators.ContentSafety
{
    public class UnsafeContentException : Exception
    {
        public SafetyClassification SafetyClassification { get; }

        public UnsafeContentException(string message, SafetyClassification safetyClassification) 
            : base(message)
        {
            SafetyClassification = safetyClassification;
        }

        public UnsafeContentException(string message) : base(message)
        {
            SafetyClassification = new SafetyClassification
            {
                IsSafe = false,
                Category = "Unknown",
                Reason = message,
                Confidence = 1.0
            };
        }
    }
}
