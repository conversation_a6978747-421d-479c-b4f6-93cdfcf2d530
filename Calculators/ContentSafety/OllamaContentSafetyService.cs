using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Calculators.ContentSafety
{
    public class OllamaContentSafetyService : IContentSafetyService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _model;

        public OllamaContentSafetyService(HttpClient? httpClient = null, string baseUrl = "http://localhost:11434", string model = "llama-guard3:8b")
        {
            _httpClient = httpClient ?? new HttpClient();
            _baseUrl = baseUrl;
            _model = model;
        }

        public async Task<SafetyClassification> ValidateContentAsync(string content)
        {
            try
            {
                var prompt = $@"Task: Check if there is unsafe content in 'User' messages in conversations according to our safety policy with the below categories.

<BEGIN UNSAFE CONTENT CATEGORIES>
O1: Violence and Hate.
Should not
- Help people plan or engage in violence.
- Encourage people to engage in violence.
- Express hateful, derogatory, or demeaning sentiments against people based on sensitive personal characteristics like their race, color, religion, national origin, sexual orientation, gender, gender identity, or disability.
- Encourage discrimination against people based on those characteristics and should not use slurs or create insults based on those characteristics.
Can
- Provide information on violence and discrimination.
- Discuss the topics of hate and violence, and can discuss historical events involving violence.
O2: Sexual Content.
Should not
- Engage in graphic and detailed discussions about sexual acts or anatomy.
- Provide graphic sexual content.
- Encourage people to engage in sexual activities.
Can
- Provide general information about sexual education, anatomy, and health.
O3: Criminal Planning.
Should not
- Help people plan or engage in criminal activities like theft, kidnapping, or financial crimes, especially when the person indicates that they may act on the information provided.
- Encourage people to engage in criminal activities or condone criminal behavior.
Can
- Help people understand how crimes are committed, provided that the response is not so detailed that it could be used to provide a roadmap for future crimes.
- Help people understand how systems work that are used to prevent crimes.
O4: Guns and Illegal Weapons.
Should not
- Assist in the planning or execution of firearm or illegal weapons crimes, especially when the person indicates that they may act on the information provided.
- Encourage people to engage in firearm or illegal weapons crimes or condone such behavior.
Can
- Discuss firearms and the arguments for and against firearm ownership.
O5: Regulated or Controlled Substances.
Should not
- Encourage or assist people in the trafficking, consumption, or use of regulated or controlled substances, including but not limited to illegal drugs, tobacco, alcohol, and cannabis.
- Assist or encourage people to create such substances when it is illegal to do so.
Can
- Discuss the use of regulated or controlled substances, and can provide general information on those substances (ex: what are the chemical and physical properties of cannabis).
- Provide information on how those substances are manufactured, provided that the information is not so detailed as to provide step-by-step instructions for their creation.
O6: Self-Harm.
Should not
- Encourage people to harm themselves.
- Romanticize or condone self-harm or suicide.
- Provide information on how to commit suicide or engage in self-harm.
- Provide any information or advice, other than information regarding appropriate health resources, to people who express or imply that they wish to harm themselves.
Can
- Provide general information regarding self-harm and suicide (ex: statistics, the mental health challenges faced by those who take these actions).
Should
- Provide information regarding appropriate health resources to people who imply or express the desire to harm themselves.
<END UNSAFE CONTENT CATEGORIES>

<BEGIN CONVERSATION>

User: {content}

<END CONVERSATION>

Provide your safety assessment for the LAST message:";

                var requestBody = new
                {
                    model = _model,
                    prompt = prompt,
                    stream = false,
                    options = new
                    {
                        temperature = 0.1,
                        top_p = 0.9
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var httpContent = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/generate", httpContent);
                
                if (!response.IsSuccessStatusCode)
                {
                    return new SafetyClassification
                    {
                        IsSafe = true, // Default to safe if service unavailable
                        Category = "ServiceUnavailable",
                        Reason = $"Content safety service unavailable: {response.StatusCode}",
                        Confidence = 0.0,
                        Content = content
                    };
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var ollamaResponse = JsonSerializer.Deserialize<OllamaResponse>(responseContent);

                if (ollamaResponse?.Response == null)
                {
                    return new SafetyClassification
                    {
                        IsSafe = true,
                        Category = "Unknown",
                        Reason = "Unable to parse safety response",
                        Confidence = 0.0,
                        Content = content
                    };
                }

                // Parse the response to determine safety
                var responseText = ollamaResponse.Response.ToLowerInvariant();
                bool isSafe = responseText.Contains("safe") && !responseText.Contains("unsafe");
                
                string category = "Safe";
                string reason = "Content appears to be safe";
                double confidence = 0.8;

                if (!isSafe)
                {
                    // Try to extract category from response
                    if (responseText.Contains("o1") || responseText.Contains("violence") || responseText.Contains("hate"))
                    {
                        category = "Violence and Hate";
                    }
                    else if (responseText.Contains("o2") || responseText.Contains("sexual"))
                    {
                        category = "Sexual Content";
                    }
                    else if (responseText.Contains("o3") || responseText.Contains("criminal"))
                    {
                        category = "Criminal Planning";
                    }
                    else if (responseText.Contains("o4") || responseText.Contains("weapon") || responseText.Contains("gun"))
                    {
                        category = "Guns and Illegal Weapons";
                    }
                    else if (responseText.Contains("o5") || responseText.Contains("substance") || responseText.Contains("drug"))
                    {
                        category = "Regulated or Controlled Substances";
                    }
                    else if (responseText.Contains("o6") || responseText.Contains("self-harm") || responseText.Contains("suicide"))
                    {
                        category = "Self-Harm";
                    }
                    else
                    {
                        category = "Unsafe";
                    }
                    
                    reason = "Content flagged as potentially unsafe";
                    confidence = 0.9;
                }

                return new SafetyClassification
                {
                    IsSafe = isSafe,
                    Category = category,
                    Reason = reason,
                    Confidence = confidence,
                    Content = content
                };
            }
            catch (Exception ex)
            {
                // Default to safe if there's an error
                return new SafetyClassification
                {
                    IsSafe = true,
                    Category = "Error",
                    Reason = $"Error during safety validation: {ex.Message}",
                    Confidence = 0.0,
                    Content = content
                };
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    internal class OllamaResponse
    {
        public string? Response { get; set; }
        public bool Done { get; set; }
    }
}
