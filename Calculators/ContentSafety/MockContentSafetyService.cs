using System;
using System.Threading.Tasks;

namespace Calculators.ContentSafety
{
    /// <summary>
    /// Mock implementation of IContentSafetyService for testing purposes.
    /// This service simulates content safety validation without requiring Ollama.
    /// </summary>
    public class MockContentSafetyService : IContentSafetyService
    {
        private readonly bool _defaultSafetyResult;

        public MockContentSafetyService(bool defaultSafetyResult = true)
        {
            _defaultSafetyResult = defaultSafetyResult;
        }

        public Task<SafetyClassification> ValidateContentAsync(string content)
        {
            // Simulate some basic unsafe content detection for testing
            var isUnsafe = content.ToLowerInvariant().Contains("unsafe") ||
                          content.ToLowerInvariant().Contains("violence") ||
                          content.ToLowerInvariant().Contains("hate") ||
                          content.ToLowerInvariant().Contains("harmful");

            var result = new SafetyClassification
            {
                IsSafe = _defaultSafetyResult && !isUnsafe,
                Category = isUnsafe ? "Violence and Hate" : "Safe",
                Reason = isUnsafe ? "Content contains potentially unsafe keywords" : "Content appears safe",
                Confidence = 0.95,
                Content = content
            };

            return Task.FromResult(result);
        }
    }
}
