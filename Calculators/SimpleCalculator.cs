using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Calculators.ContentSafety;

namespace Calculators
{
    public class SimpleCalculator
    {
        private decimal _currentValue;
        private readonly IContentSafetyService? _safetyService;
        private readonly List<SafetyClassification> _safetyLog;

        public SimpleCalculator()
        {
            _currentValue = 0;
            _safetyLog = new List<SafetyClassification>();
        }

        public SimpleCalculator(IContentSafetyService safetyService)
        {
            _currentValue = 0;
            _safetyService = safetyService;
            _safetyLog = new List<SafetyClassification>();
        }

        public SimpleCalculator Enter(decimal value)
        {
            _currentValue = value;
            return this;
        }

        public SimpleCalculator Plus(decimal value)
        {
            _currentValue += value;
            return this;
        }

        public SimpleCalculator Minus(decimal value)
        {
            _currentValue -= value;
            return this;
        }

        public SimpleCalculator Times(decimal value)
        {
            _currentValue *= value;
            return this;
        }

        public SimpleCalculator DividedBy(decimal value)
        {
            if (value == 0)
                throw new DivideByZeroException("Cannot divide by zero");
            
            _currentValue /= value;
            return this;
        }

        public decimal Equals()
        {
            return _currentValue;
        }

        public void Reset()
        {
            _currentValue = 0;
        }

        public async Task<decimal> EqualsAsync()
        {
            var result = Equals();

            if (_safetyService != null)
            {
                var resultText = $"The calculation result is {result}";
                var safetyResult = await _safetyService.ValidateContentAsync(resultText);
                _safetyLog.Add(safetyResult);

                if (!safetyResult.IsSafe)
                {
                    throw new UnsafeContentException($"Content validation failed: {safetyResult.Category}", safetyResult);
                }
            }

            return result;
        }

        public IReadOnlyList<SafetyClassification> GetSafetyLog()
        {
            return _safetyLog.AsReadOnly();
        }

        public async Task<SafetyClassification> ValidateMessageAsync(string message)
        {
            if (_safetyService == null)
                throw new InvalidOperationException("Content safety service not configured");

            var result = await _safetyService.ValidateContentAsync(message);
            _safetyLog.Add(result);
            return result;
        }
    }
}
