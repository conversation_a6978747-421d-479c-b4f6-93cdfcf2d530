using System;
using System.Collections.Generic;
// using Calculators.ContentSafety;

namespace Calculators
{
    public class SimpleCalculator
    {
        private decimal _currentValue;

        public SimpleCalculator()
        {
            _currentValue = 0;
        }

        public SimpleCalculator Enter(decimal value)
        {
            _currentValue = value;
            return this;
        }

        public SimpleCalculator Plus(decimal value)
        {
            _currentValue += value;
            return this;
        }

        public SimpleCalculator Minus(decimal value)
        {
            _currentValue -= value;
            return this;
        }

        public SimpleCalculator Times(decimal value)
        {
            _currentValue *= value;
            return this;
        }

        public SimpleCalculator DividedBy(decimal value)
        {
            if (value == 0)
                throw new DivideByZeroException("Cannot divide by zero");
            
            _currentValue /= value;
            return this;
        }

        public decimal Equals()
        {
            return _currentValue;
        }

        public void Reset()
        {
            _currentValue = 0;
        }

        // TODO: Students will add content safety integration here in future exercises
        // private readonly IContentSafetyService _safetyService;
        // private readonly List<SafetyClassification> _safetyLog;
        
        // public SimpleCalculator(IContentSafetyService safetyService)
        // {
        //     _safetyService = safetyService;
        //     _safetyLog = new List<SafetyClassification>();
        // }

        // public async Task<decimal> EqualsAsync()
        // {
        //     // Implementation for content safety validation
        // }

        // public IReadOnlyList<SafetyClassification> GetSafetyLog()
        // {
        //     return _safetyLog.AsReadOnly();
        // }

        // public async Task<SafetyClassification> ValidateMessageAsync(string message)
        // {
        //     // Implementation for message validation
        // }
    }
}
