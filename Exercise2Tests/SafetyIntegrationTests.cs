using NUnit.Framework;
using Calculators;
using Calculators.ContentSafety;
using System.Threading.Tasks;
using System.Linq;

namespace Exercise2Tests
{
    [TestFixture]
    public class SafetyIntegrationTests
    {
        private SimpleCalculator? _calculator;
        private MockContentSafetyService? _safetyService;

        [SetUp]
        public void Setup()
        {
            _safetyService = new MockContentSafetyService(defaultSafetyResult: true);
            _calculator = new SimpleCalculator(_safetyService);
            _calculator.Enter(4);
        }

        [TearDown]
        public void Teardown()
        {
            _calculator?.Reset();
        }

        [Test]
        public async Task BasicSafetyTest()
        {
            // Arrange - calculator already has 4 from setup
            
            // Act
            var result = await _calculator!.Plus(2).EqualsAsync();
            
            // Assert
            Assert.That(result, Is.EqualTo(6));
            
            // Verify safety log
            var safetyLog = _calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            Assert.That(safetyLog[0].<PERSON><PERSON><PERSON><PERSON>, <PERSON>.True);
            Assert.That(safetyLog[0].Content, Is.EqualTo("The calculation result is 6"));
        }

        [Test]
        public async Task MultipleOperationsTest()
        {
            // Arrange - calculator already has 4 from setup
            
            // Act - perform multiple operations
            await _calculator!.Plus(2).EqualsAsync();
            _calculator.Reset();
            _calculator.Enter(10);
            await _calculator.Minus(3).EqualsAsync();
            
            // Assert
            var safetyLog = _calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(2));
            
            // First operation
            Assert.That(safetyLog[0].IsSafe, Is.True);
            Assert.That(safetyLog[0].Content, Is.EqualTo("The calculation result is 6"));
            
            // Second operation
            Assert.That(safetyLog[1].IsSafe, Is.True);
            Assert.That(safetyLog[1].Content, Is.EqualTo("The calculation result is 7"));
        }

        [Test]
        public async Task SafeMessageValidationTest()
        {
            // Arrange
            var safeMessage = "This is a safe calculation message";
            
            // Act
            var result = await _calculator!.ValidateMessageAsync(safeMessage);
            
            // Assert
            Assert.That(result.IsSafe, Is.True);
            Assert.That(result.Category, Is.EqualTo("Safe"));
            Assert.That(result.Content, Is.EqualTo(safeMessage));
            
            // Verify it's logged
            var safetyLog = _calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            Assert.That(safetyLog[0], Is.EqualTo(result));
        }

        [Test]
        public async Task UnsafeMessageValidationTest()
        {
            // Arrange
            var unsafeMessage = "This message contains violence and hate";
            
            // Act
            var result = await _calculator!.ValidateMessageAsync(unsafeMessage);
            
            // Assert
            Assert.That(result.IsSafe, Is.False);
            Assert.That(result.Category, Is.EqualTo("Violence and Hate"));
            Assert.That(result.Content, Is.EqualTo(unsafeMessage));
            
            // Verify it's logged
            var safetyLog = _calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            Assert.That(safetyLog[0], Is.EqualTo(result));
        }

        [Test]
        public async Task UnsafeContentThrowsExceptionTest()
        {
            // Arrange - create calculator with service that flags content as unsafe
            var unsafeSafetyService = new MockContentSafetyService(defaultSafetyResult: false);
            var unsafeCalculator = new SimpleCalculator(unsafeSafetyService);
            unsafeCalculator.Enter(4);
            
            // Act & Assert
            var exception = await Assert.ThrowsAsync<UnsafeContentException>(
                async () => await unsafeCalculator.Plus(2).EqualsAsync()
            );
            
            Assert.That(exception.Message, Does.Contain("Content validation failed"));
            Assert.That(exception.SafetyClassification.IsSafe, Is.False);
        }

        [Test]
        public void ValidateMessageWithoutSafetyServiceThrowsException()
        {
            // Arrange - calculator without safety service
            var calculatorWithoutSafety = new SimpleCalculator();
            
            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(
                async () => await calculatorWithoutSafety.ValidateMessageAsync("test message")
            );
            
            Assert.That(exception!.Message, Does.Contain("Content safety service not configured"));
        }

        [Test]
        public async Task SafetyLogPersistsAcrossOperations()
        {
            // Arrange - calculator already has 4 from setup
            
            // Act - perform multiple operations and validations
            await _calculator!.Plus(1).EqualsAsync(); // Result: 5
            await _calculator.ValidateMessageAsync("Safe message 1");
            _calculator.Reset();
            _calculator.Enter(10);
            await _calculator.Times(2).EqualsAsync(); // Result: 20
            await _calculator.ValidateMessageAsync("Safe message 2");
            
            // Assert
            var safetyLog = _calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(4));
            
            // Verify all entries
            Assert.That(safetyLog[0].Content, Is.EqualTo("The calculation result is 5"));
            Assert.That(safetyLog[1].Content, Is.EqualTo("Safe message 1"));
            Assert.That(safetyLog[2].Content, Is.EqualTo("The calculation result is 20"));
            Assert.That(safetyLog[3].Content, Is.EqualTo("Safe message 2"));
            
            // All should be safe
            Assert.That(safetyLog.All(log => log.IsSafe), Is.True);
        }

        [Test]
        public async Task CalculatorWithoutSafetyServiceWorksNormally()
        {
            // Arrange - calculator without safety service
            var normalCalculator = new SimpleCalculator();
            normalCalculator.Enter(4);
            
            // Act - async equals should work without safety validation
            var result = await normalCalculator.Plus(2).EqualsAsync();
            
            // Assert
            Assert.That(result, Is.EqualTo(6));
            
            // Safety log should be empty
            var safetyLog = normalCalculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(0));
        }
    }
}
