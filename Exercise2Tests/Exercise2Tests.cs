using NUnit.Framework;
using Calculators;

namespace Exercise2Tests
{
    [TestFixture]
    public class Exercise2Tests
    {
        private SimpleCalculator? _calculator;

        // TODO: Step 1 - Add Setup method
        // Use the appropriate NUnit attribute to mark this method as setup
        // This method should:
        // - Create a new SimpleCalculator object
        // - Initialize it with the number 4
        // - Store it in the _calculator field
        
        // [TODO: Add appropriate NUnit attribute here]
        // public void Setup()
        // {
        //     // TODO: Initialize _calculator with a new SimpleCalculator
        //     // TODO: Set the initial value to 4 using Enter(4)
        // }

        // TODO: Step 2 - Add Teardown method  
        // Use the appropriate NUnit attribute to mark this method as teardown
        // This method should:
        // - Reset the calculator after each test
        
        // [TODO: Add appropriate NUnit attribute here]
        // public void Teardown()
        // {
        //     // TODO: Reset the calculator
        // }

        [Test]
        public void AdditionTest()
        {
            // TODO: Step 3 - Edit this test to use the setup calculator
            // The calculator should already have 4 from setup
            // Add 2 and check that result is 6
            
            // Current implementation (to be modified):
            var calculator = new SimpleCalculator();
            var result = calculator.Enter(4).Plus(2).Equals();
            Assert.That(result, Is.EqualTo(6));
        }

        [Test]
        public void SubtractionTest()
        {
            // TODO: Step 5 - Modify this test to use the setup calculator
            // The calculator should already have 4 from setup
            // Subtract 2 and check that result is 2
            
            // Current implementation (to be modified):
            var calculator = new SimpleCalculator();
            var result = calculator.Enter(4).Minus(2).Equals();
            Assert.That(result, Is.EqualTo(2));
        }

        [Test]
        public void MultiplicationTest()
        {
            // TODO: Step 5 - Modify this test to use the setup calculator
            // The calculator should already have 4 from setup
            // Multiply by 2 and check that result is 8
            
            // Current implementation (to be modified):
            var calculator = new SimpleCalculator();
            var result = calculator.Enter(4).Times(2).Equals();
            Assert.That(result, Is.EqualTo(8));
        }

        [Test]
        public void DivisionTest()
        {
            // TODO: Step 5 - Modify this test to use the setup calculator
            // The calculator should already have 4 from setup
            // Divide by 2 and check that result is 2
            
            // Current implementation (to be modified):
            var calculator = new SimpleCalculator();
            var result = calculator.Enter(4).DividedBy(2).Equals();
            Assert.That(result, Is.EqualTo(2));
        }

        [Test]
        public void DivisionByZeroTest()
        {
            // TODO: Step 5 - Modify this test to use the setup calculator
            // Test that dividing by zero throws an exception
            
            // Current implementation (to be modified):
            var calculator = new SimpleCalculator();
            calculator.Enter(4);
            Assert.Throws<DivideByZeroException>(() => calculator.DividedBy(0));
        }
    }
}
