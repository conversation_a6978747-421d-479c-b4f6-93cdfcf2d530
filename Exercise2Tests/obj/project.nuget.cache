{"version": 2, "dgSpecHash": "5eJtS4FRE6Y=", "success": true, "projectFilePath": "/home/<USER>/WTC/QA-meta/exercise-2-calculator/Exercise2Tests/Exercise2Tests.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/coverlet.collector/6.0.0/coverlet.collector.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.codecoverage/17.8.0/microsoft.codecoverage.17.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.net.test.sdk/17.8.0/microsoft.net.test.sdk.17.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.testplatform.objectmodel/17.8.0/microsoft.testplatform.objectmodel.17.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.testplatform.testhost/17.8.0/microsoft.testplatform.testhost.17.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/newtonsoft.json/13.0.1/newtonsoft.json.13.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/nuget.frameworks/6.5.0/nuget.frameworks.6.5.0.nupkg.sha512", "/home/<USER>/.nuget/packages/nunit/4.0.1/nunit.4.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/nunit.analyzers/3.9.0/nunit.analyzers.3.9.0.nupkg.sha512", "/home/<USER>/.nuget/packages/nunit3testadapter/4.5.0/nunit3testadapter.4.5.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.reflection.metadata/1.6.0/system.reflection.metadata.1.6.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.encodings.web/8.0.0/system.text.encodings.web.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.json/8.0.0/system.text.json.8.0.0.nupkg.sha512"], "logs": []}