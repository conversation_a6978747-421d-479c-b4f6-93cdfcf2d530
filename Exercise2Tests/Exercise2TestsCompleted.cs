using NUnit.Framework;
using Calculators;

namespace Exercise2Tests
{
    /// <summary>
    /// This is the completed version showing how the tests should look after implementing
    /// setup and teardown methods. Students should NOT look at this file until they've
    /// completed the exercise themselves.
    /// </summary>
    [TestFixture]
    public class Exercise2TestsCompleted
    {
        private SimpleCalculator? _calculator;

        [SetUp]
        public void Setup()
        {
            _calculator = new SimpleCalculator();
            _calculator.Enter(4);
        }

        [TearDown]
        public void Teardown()
        {
            _calculator?.Reset();
        }

        [Test]
        public void AdditionTest()
        {
            // Calculator already has 4 from setup
            var result = _calculator!.Plus(2).Equals();
            Assert.That(result, Is.EqualTo(6));
        }

        [Test]
        public void SubtractionTest()
        {
            // Calculator already has 4 from setup
            var result = _calculator!.Minus(2).Equals();
            Assert.That(result, Is.EqualTo(2));
        }

        [Test]
        public void MultiplicationTest()
        {
            // Calculator already has 4 from setup
            var result = _calculator!.Times(2).Equals();
            Assert.That(result, Is.EqualTo(8));
        }

        [Test]
        public void DivisionTest()
        {
            // Calculator already has 4 from setup
            var result = _calculator!.DividedBy(2).Equals();
            Assert.That(result, Is.EqualTo(2));
        }

        [Test]
        public void DivisionByZeroTest()
        {
            // Calculator already has 4 from setup
            Assert.Throws<DivideByZeroException>(() => _calculator!.DividedBy(0));
        }
    }
}
