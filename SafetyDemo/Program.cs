using Calculators;
using Calculators.ContentSafety;

namespace SafetyDemo
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== Calculator with Content Safety Demo ===");
            Console.WriteLine();

            // Demo basic calculator without safety
            Console.WriteLine("1. Basic Calculator Operations (No Safety):");
            var basicCalculator = new SimpleCalculator();

            var result1 = basicCalculator.Enter(4).Plus(2).Equals();
            Console.WriteLine($"4 + 2 = {result1}");

            basicCalculator.Reset();
            var result2 = basicCalculator.Enter(4).Minus(2).Equals();
            Console.WriteLine($"4 - 2 = {result2}");

            Console.WriteLine();
            Console.WriteLine("2. Calculator with Content Safety Integration:");

            // Create calculator with mock safety service for demo
            var safetyService = new MockContentSafetyService(defaultSafetyResult: true);
            var safeCalculator = new SimpleCalculator(safetyService);

            try
            {
                var safeResult = await safeCalculator.Enter(4).Plus(2).EqualsAsync();
                Console.WriteLine($"4 + 2 = {safeResult} (Content validated as safe)");

                // Show safety log
                var safetyLog = safeCalculator.GetSafetyLog();
                Console.WriteLine($"Safety log entries: {safetyLog.Count}");
                foreach (var entry in safetyLog)
                {
                    Console.WriteLine($"  - Content: '{entry.Content}' | Safe: {entry.IsSafe} | Category: {entry.Category}");
                }
            }
            catch (UnsafeContentException ex)
            {
                Console.WriteLine($"Content safety violation: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("3. Message Validation Demo:");

            try
            {
                var safeMessage = await safeCalculator.ValidateMessageAsync("This is a safe calculation message");
                Console.WriteLine($"Safe message validation: {safeMessage.IsSafe} ({safeMessage.Category})");

                var unsafeMessage = await safeCalculator.ValidateMessageAsync("This message contains violence and hate");
                Console.WriteLine($"Unsafe message validation: {unsafeMessage.IsSafe} ({unsafeMessage.Category})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Message validation error: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("4. Error Handling:");
            try
            {
                basicCalculator.Reset();
                basicCalculator.Enter(10).DividedBy(0);
            }
            catch (DivideByZeroException ex)
            {
                Console.WriteLine($"Division by zero handled: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("5. Ollama Integration (Optional):");
            Console.WriteLine("To use real Ollama integration:");
            Console.WriteLine("1. Install Ollama: https://ollama.ai");
            Console.WriteLine("2. Run: ollama pull llama-guard3:8b");
            Console.WriteLine("3. Run: ollama serve");
            Console.WriteLine("4. Replace MockContentSafetyService with OllamaContentSafetyService");

            Console.WriteLine();
            Console.WriteLine("Demo completed successfully!");
        }
    }
}
