{"format": 1, "restore": {"/home/<USER>/WTC/QA-meta/exercise-2-calculator/SafetyDemo/SafetyDemo.csproj": {}}, "projects": {"/home/<USER>/WTC/QA-meta/exercise-2-calculator/Calculators/Calculators.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/WTC/QA-meta/exercise-2-calculator/Calculators/Calculators.csproj", "projectName": "Calculators", "projectPath": "/home/<USER>/WTC/QA-meta/exercise-2-calculator/Calculators/Calculators.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/WTC/QA-meta/exercise-2-calculator/Calculators/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"System.Text.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/snap/dotnet-sdk/256/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/WTC/QA-meta/exercise-2-calculator/SafetyDemo/SafetyDemo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/WTC/QA-meta/exercise-2-calculator/SafetyDemo/SafetyDemo.csproj", "projectName": "SafetyDemo", "projectPath": "/home/<USER>/WTC/QA-meta/exercise-2-calculator/SafetyDemo/SafetyDemo.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/WTC/QA-meta/exercise-2-calculator/SafetyDemo/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/home/<USER>/WTC/QA-meta/exercise-2-calculator/Calculators/Calculators.csproj": {"projectPath": "/home/<USER>/WTC/QA-meta/exercise-2-calculator/Calculators/Calculators.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/snap/dotnet-sdk/256/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}